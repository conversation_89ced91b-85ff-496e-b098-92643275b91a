import os
import sys
import time
import argparse
from collections import defaultdict
from scapy.all import sniff, IP, UDP
import netifaces
import logging

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('RadarScanner')

def get_all_physical_interfaces():
    """获取所有物理网络接口（排除虚拟接口）"""
    physical_interfaces = []
    virtual_keywords = ['loopback', 'virtual', 'vpn', 'tunnel', 'bluetooth', 'teredo', 'isatap']
    
    for iface in netifaces.interfaces():
        # 检查是否是虚拟接口
        if any(kw in iface.lower() for kw in virtual_keywords):
            continue
            
        # 检查是否有IPv4地址
        try:
            addresses = netifaces.ifaddresses(iface)
            if netifaces.AF_INET in addresses:
                # 确保不是回环地址
                for addr_info in addresses[netifaces.AF_INET]:
                    if not addr_info['addr'].startswith('127.'):
                        physical_interfaces.append(iface)
                        break
        except Exception:
            continue
            
    return physical_interfaces

def get_interface_ip(iface):
    """获取指定网络接口的IP地址"""
    try:
        addresses = netifaces.ifaddresses(iface)
        if netifaces.AF_INET in addresses:
            for addr_info in addresses[netifaces.AF_INET]:
                if not addr_info['addr'].startswith('127.'):
                    return addr_info['addr']
    except Exception as e:
        logger.error(f"Error getting IP for {iface}: {e}")
    return None

def main():
    parser = argparse.ArgumentParser(description='Lidar Target Auto-Detector for Windows')
    parser.add_argument('-i', '--interface', help='Network interface name')
    parser.add_argument('-t', '--time', type=int, default=5, help='Scan duration in seconds (default: 5)')
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    args = parser.parse_args()

    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # 获取所有物理接口
    physical_ifaces = get_all_physical_interfaces()
    
    if not physical_ifaces:
        logger.error("No physical network interfaces found. Check your network connections.")
        logger.error("Detected interfaces: %s", netifaces.interfaces())
        return
    
    # 如果没有指定接口，让用户选择
    target_iface = args.interface
    if not target_iface:
        logger.info("Available physical interfaces:")
        for i, iface in enumerate(physical_ifaces, 1):
            ip = get_interface_ip(iface)
            logger.info(f"{i}. {iface} - IP: {ip if ip else 'No IP address'}")
        
        try:
            choice = int(input("\nSelect interface number: "))
            if 1 <= choice <= len(physical_ifaces):
                target_iface = physical_ifaces[choice-1]
            else:
                logger.warning("Invalid selection. Using first interface.")
                target_iface = physical_ifaces[0]
        except:
            logger.warning("Invalid input. Using first interface.")
            target_iface = physical_ifaces[0]
    
    # 获取本机IP
    local_ip = get_interface_ip(target_iface)
    if not local_ip:
        logger.error(f"Could not get valid IP for interface {target_iface}")
        logger.error("Make sure the interface is connected and has a valid IP address.")
        return
    
    logger.info(f"\nScanning on interface: {target_iface}")
    logger.info(f"Local IP address: {local_ip}")
    logger.info(f"Scan duration: {args.time} seconds...")
    logger.info("Please wait while capturing radar traffic...")
    
    # 创建统计字典
    target_counter = defaultdict(int)
    
    # 数据包处理函数
    def process_packet(pkt):
        if IP in pkt and UDP in pkt:
            src_ip = pkt[IP].src
            dst_ip = pkt[IP].dst
            dst_port = pkt[UDP].dport
            
            # 排除广播/组播地址
            if not dst_ip.startswith("239.") and not dst_ip == "***************":
                # 创建唯一标识符 (雷达IP+目标IP+目标端口)
                identifier = f"{src_ip}->{dst_ip}:{dst_port}"
                target_counter[identifier] += 1
                if args.verbose:
                    logger.debug(f"Captured packet: {identifier}")
    
    # 开始捕获
    start_time = time.time()
    try:
        sniff(
            iface=target_iface,
            prn=process_packet,
            filter="udp",
            store=0,
            timeout=args.time
        )
    except Exception as e:
        if "No permission" in str(e) or "Permission denied" in str(e):
            logger.error("\nERROR: Permission denied for packet capture.")
            logger.error("Please run PowerShell as Administrator and try again.")
            return
        else:
            logger.error(f"\nERROR: {str(e)}")
            return
    
    # 分析结果
    if not target_counter:
        logger.warning("\nNo radar traffic detected. Possible reasons:")
        logger.warning("1. Radar is not connected or powered on")
        logger.warning("2. Wrong network interface selected")
        logger.warning("3. Firewall blocking UDP traffic")
        logger.warning("4. Radar is using TCP instead of UDP")
        return
    
    # 找出最高频的组合
    most_common = max(target_counter.items(), key=lambda x: x[1])
    total_packets = sum(target_counter.values())
    
    # 解析结果
    identifier, count = most_common
    radar_ip, target_info = identifier.split('->')
    target_ip, target_port = target_info.split(':')
    
    # 计算占比
    percentage = (count / total_packets) * 100
    
    logger.info("\n" + "="*50)
    logger.info("🔥 LIDAR CONFIGURATION DETECTED 🔥")
    logger.info("="*50)
    logger.info(f"Radar IP:        {radar_ip}")
    logger.info(f"Target IP:       {target_ip}")
    logger.info(f"Data Port:       {target_port}")
    logger.info(f"Confidence:      {percentage:.1f}% ({count}/{total_packets} packets)")
    logger.info("="*50)
    
    # 显示前3名结果
    logger.info("\nTop 3 detected targets:")
    sorted_targets = sorted(target_counter.items(), key=lambda x: x[1], reverse=True)[:3]
    for i, (identifier, count) in enumerate(sorted_targets, 1):
        _, target_info = identifier.split('->')
        target_ip, target_port = target_info.split(':')
        logger.info(f"{i}. {target_ip}:{target_port} - {count} packets")
    
    # 检查是否为常见激光雷达端口
    common_ports = {
        2368: "Velodyne/Hesai", 
        2369: "Velodyne/Hesai", 
        6699: "RoboSense", 
        7788: "RoboSense", 
        8010: "Ouster", 
        7502: "Livox"
    }
    
    try:
        port_int = int(target_port)
        if port_int in common_ports:
            logger.info(f"\nNOTE: Port {target_port} is commonly used by {common_ports[port_int]} lidars")
        else:
            logger.info(f"\nNOTE: Port {target_port} is not a standard lidar port")
    except:
        logger.info(f"\nNOTE: Detected port: {target_port}")

if __name__ == "__main__":
    logger.info("Radar Scanner - Starting...")
    main()